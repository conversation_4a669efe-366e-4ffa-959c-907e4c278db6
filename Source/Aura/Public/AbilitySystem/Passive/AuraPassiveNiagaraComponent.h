// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "NiagaraComponent.h"
#include "AuraPassiveNiagaraComponent.generated.h"

/**
 * 
 */
UCLASS()
class AURA_API UAuraPassiveNiagaraComponent : public UNiagaraComponent
{
	GENERATED_BODY()

public:
	UAuraPassiveNiagaraComponent();

protected:
	virtual void BeginPlay() override;

	UFUNCTION()
	void OnDeactivatePassiveAbility(const FGameplayTag& AbilityTag);
	UFUNCTION()
	void OnActivatePassiveAbility(const FGameplayTag& AbilityTag);

	UPROPERTY(EditAnywhere, Category = "AbilityTag")
	FGameplayTag AbilityTag;
};
