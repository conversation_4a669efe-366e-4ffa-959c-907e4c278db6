#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "Kismet/BlueprintAsyncActionBase.h"
#include "WaitCooldownChange.generated.h"

class UAbilitySystemComponent;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FCooldownChangeSignature, float, Cooldown);

/**
 * 等待某个冷却标签开始和结束的 AsyncTask。
 */
UCLASS(BlueprintType, meta = (ExposedAsyncProxy = "AsyncTask"))
class AURA_API UWaitCooldownChange : public UBlueprintAsyncActionBase
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, meta = (BlueprintInternalUseOnly = "true"), Category = "Cooldown")
	static UWaitCooldownChange* WaitCooldownChange(const FGameplayTag& InCooldownTag,
	                                               UAbilitySystemComponent* ASC);

	UPROPERTY(BlueprintAssignable, Category = "Cooldown")
	FCooldownChangeSignature OnCooldownStart;

	UPROPERTY(BlueprintAssignable, Category = "Cooldown")
	FCooldownChangeSignature OnCooldownEnd;

	UFUNCTION(BlueprintCallable)
	void EndWaitCooldownChange();

protected:
	// 析构函数确保委托被正确清理
	virtual void BeginDestroy() override;

private:
	void CooldownTagChanged(FGameplayTag Tag, int32 NewCount);

	// 清理所有委托的辅助方法
	void CleanupDelegates();

	UPROPERTY()
	UAbilitySystemComponent* AbilitySystemComponent;

	FDelegateHandle CooldownEventHandle;
	FDelegateHandle EffectAddedDelegateHandle;
};
