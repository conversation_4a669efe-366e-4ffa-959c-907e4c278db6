// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystemComponent.h"
#include "AuraAbilitySystemComponent.generated.h"

struct FAuraAbilityInfo;
class UAuraAbilitySystemComponent;
DECLARE_MULTICAST_DELEGATE_OneParam(FEffectAssets, const FGameplayTagContainer& Tags);
DECLARE_MULTICAST_DELEGATE(FGivenAbility);
DECLARE_DELEGATE_OneParam(FForEachAbility, const FGameplayAbilitySpec& AbilitySpec);
DECLARE_MULTICAST_DELEGATE_ThreeParams(FAbilityStatusChangedSignature, const FGameplayTag& AbilityTag,
                                       const FGameplayTag& StatusTag, const int32 Level);
DECLARE_DELEGATE(FExecuteSomethingSignature);
DECLARE_MULTICAST_DELEGATE_ThreeParams(FActivateButtonDelegateClient, const FGameplayTag& InTag,
                                       const FString& Description, const FString& NextLevelDescription);
DECLARE_MULTICAST_DELEGATE_TwoParams(FWaitingForAbilitySignatureClient, const FGameplayTag& AbilityTag,
                                     const FGameplayTag& TypeTag);
DECLARE_MULTICAST_DELEGATE_OneParam(FClearEquippedAbilityClientSignature, const FGameplayTag& SlotTag);
DECLARE_MULTICAST_DELEGATE_TwoParams(FAbilityInfoSignatureClient, const FAuraAbilityInfo& Info, const int32 AbilityLevel);
DECLARE_MULTICAST_DELEGATE_OneParam(FDeactivatedAbilitySignature, const FGameplayTag& AbilityTag);
DECLARE_MULTICAST_DELEGATE_TwoParams(FPassiveAbilityStatusChangedSignature, const FGameplayTag& AbilityTag, const bool bActivate);

/**
 * 
 */
UCLASS()
class AURA_API UAuraAbilitySystemComponent : public UAbilitySystemComponent
{
	GENERATED_BODY()

public:
	UFUNCTION()
	void AbilityActorInfoSet();

	void AddCharacterAbilities(const TArray<TSubclassOf<UGameplayAbility>>& StartupAbilities);
	void AddCharacterPassiveAbilities(const TArray<TSubclassOf<UGameplayAbility>>& PassiveAbilities);
	void UpdateAbilityStatusByLevel(int32 Level);

	void AbilityInputTagPressed(const FGameplayTag& InputTag);
	void AbilityInputTagHeld(const FGameplayTag& InputTag);
	void AbilityInputTagReleased(const FGameplayTag& InputTag);

	FEffectAssets OnEffectAssets;
	FGivenAbility OnGivenAbility;
	FAbilityStatusChangedSignature OnAbilityStatusChanged;
	FActivateButtonDelegateClient OnActivateAbilityButton;
	FWaitingForAbilitySignatureClient OnWaitingForAbilityDelegate;
	FAbilityInfoSignatureClient OnEquippedAbilityStatusChanged;
	FClearEquippedAbilityClientSignature OnClearEquippedAbilityDelegate;
	FDeactivatedAbilitySignature OnDeactivatePassiveAbilityDelegate;
	FPassiveAbilityStatusChangedSignature OnPassiveAbilityStatusChangedDelegate;

	UPROPERTY()
	bool bStartupAbilitiesGiven = false;

	void ForEachAbility(FForEachAbility Delegate);

	/* Utils */
	static FGameplayTag GetAbilityTagFromSpec(const FGameplayAbilitySpec& AbilitySpec);
	static FGameplayTag GetInputTagFromSpec(const FGameplayAbilitySpec& AbilitySpec);
	static FGameplayTag GetStatusTagFromSpec(const FGameplayAbilitySpec& AbilitySpec);
	
	FGameplayTag GetTypeTagFromSpec(const FGameplayAbilitySpec& AbilitySpec);
	FGameplayTag GetStatusTagFromAbilityTag(const FGameplayTag& AbilityTag);
	FGameplayAbilitySpec* GetSpecFromAbilityTag(const FGameplayTag& AbilityTag);
	FGameplayTag GetTypeTagFromAbilityTag(const FGameplayTag& AbilityTag);
	FGameplayAbilitySpec* GetSpecFromInputTag(const FGameplayTag& InputTag);
	/* Utils end */

	UFUNCTION(Server, Reliable, BlueprintCallable)
	void UpgradeAttribute(const FGameplayTag& AttributeTag);

	UFUNCTION(Server, Reliable)
	void Server_SpendSpellPoint(const FGameplayTag& AbilityTag);

	UFUNCTION(Server, Reliable)
	void Server_SelectSpell(const FGameplayTag& AbilityTag);

	UFUNCTION(Server, Reliable)
	void Server_WaitingForEquipAbility(const FGameplayTag& AbilityTag);

	UFUNCTION(Server, Reliable)
	void Server_EquipAbility(const FGameplayTag& AbilityTag, const FGameplayTag& SlotTag);


	bool GetAbilityDescriptions(const FGameplayTag& AbilityTag, FString& Description,
	                            FString& NextLevelDescription);

	FExecuteSomethingSignature ExecuteSomethingDelegate;

protected:
	virtual void OnRep_ActivateAbilities() override;

private:
	UFUNCTION(Client, Reliable)
	void Client_OnGameplayEffectApplied(UAbilitySystemComponent* ASC, const FGameplayEffectSpec& EffectSpec,
	                                    FActiveGameplayEffectHandle ActiveGameplayEffectHandle);

	UFUNCTION(Client, Reliable)
	void Client_OnAbilityStatusChanged(const FGameplayTag& AbilityTag, const FGameplayTag& StatusTag,
	                                   const int32 Level);

	UFUNCTION(Client, Reliable)
	void Client_OnSelectSpell(const FGameplayTag& AbilityTag, const FString& Description,
	                          const FString& NextLevelDescription);

	UFUNCTION(Client, Reliable)
	void Client_OnWaitingForEquipAbility(const FGameplayTag& AbilityTag, const FGameplayTag& TypeTag);

	UFUNCTION(Client, Reliable)
	void Client_OnEquipAbility(const FAuraAbilityInfo& Info, const int32 AbilityLevel);

	UFUNCTION(Client, Reliable)
	void Client_OnClearEquippedAbility(const FGameplayTag& SlotTag);

	UFUNCTION(Client, Reliable)
	void Client_OnPassiveAbilityStatusChanged(const FGameplayTag& AbilityTag, const bool bActivate);
};
