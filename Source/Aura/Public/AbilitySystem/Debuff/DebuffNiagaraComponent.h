// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "NiagaraComponent.h"
#include "DebuffNiagaraComponent.generated.h"

/**
 * 
 */
UCLASS()
class AURA_API UDebuffNiagaraComponent : public UNiagaraComponent
{
	GENERATED_BODY()

public:
	UDebuffNiagaraComponent();

	UPROPERTY(EditDefaultsOnly, Category = "Debuff")
	FGameplayTag DebuffTag;

protected:
	virtual void BeginPlay() override;

private:
	UPROPERTY()
	bool bIsDebuffed = false;

	UFUNCTION()
	void OnDebuffTagChanged (FGameplayTag Tag, int32 NewCount);
	UFUNCTION()
	void OnDie();
};
