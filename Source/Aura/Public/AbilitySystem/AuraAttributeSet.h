// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AttributeSet.h"
#include "AbilitySystemComponent.h"

#include "AuraAttributeSet.generated.h"

#define ATTRIBUTE_ACCESSORS(ClassName, PropertyName) \
	GAMEPLAYATTRIBUTE_PROPERTY_GETTER(ClassName, PropertyName) \
	GAMEPLAYATTRIBUTE_VALUE_GETTER(PropertyName) \
	GAMEPLAYATTRIBUTE_VALUE_SETTER(PropertyName) \
	GAMEPLAYATTRIBUTE_VALUE_INITTER(PropertyName)

class UDamageTextComponent;
DECLARE_DELEGATE_RetVal(FGameplayAttribute, FGetGameplayAttribute);

typedef TBaseStaticDelegateInstance<FGameplayAttribute(), FDefaultDelegateUserPolicy>::FFuncPtr
FGetGameplayAttributeDelegate;
template <class T>
using TAttributeFuncPtr = typename TBaseStaticDelegateInstance<
	T, FDefaultDelegateUserPolicy>::FFuncPtr;

USTRUCT(BlueprintType)
struct FEffectProperties
{
	GENERATED_BODY()

	FEffectProperties()
	{
	}

	UPROPERTY(VisibleAnywhere)
	UAbilitySystemComponent* SourceASC = nullptr;
	UPROPERTY(VisibleAnywhere)
	UAbilitySystemComponent* TargetASC = nullptr;
	UPROPERTY(VisibleAnywhere)
	AActor* SourceAvatarActor = nullptr;
	UPROPERTY(VisibleAnywhere)
	AActor* TargetAvatarActor = nullptr;
	UPROPERTY(VisibleAnywhere)
	AController* SourceController = nullptr;
	UPROPERTY(VisibleAnywhere)
	AController* TargetController = nullptr;
	UPROPERTY(VisibleAnywhere)
	ACharacter* SourceCharacter = nullptr;
	UPROPERTY(VisibleAnywhere)
	ACharacter* TargetCharacter = nullptr;
	UPROPERTY(VisibleAnywhere)
	FGameplayEffectContextHandle ContextHandle;
};

/**
 * 
 */
UCLASS()
class AURA_API UAuraAttributeSet : public UAttributeSet
{
	GENERATED_BODY()

public:
	UAuraAttributeSet();

	/* UAttributeSet */
	virtual void GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const override;
	virtual void PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue) override;
	virtual void PostGameplayEffectExecute(const struct FGameplayEffectModCallbackData& Data) override;
	virtual void PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue) override;
	/* UAttributeSet end */

	bool bTopOffHealth = false;
	bool bTopOffMana = false;

	// TMap<FGameplayTag, FGetGameplayAttribute> TagsToAttributes;

	// TMap<FGameplayTag, TAttributeFuncPtr<FGameplayAttribute()>> TagsToAttributes;
	TMap<FGameplayTag, FGameplayAttribute(*)()> TagsToAttributes;

	// Debuff GameplayEffect Classes
	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects")
	TSubclassOf<UGameplayEffect> StunDebuffClass;

	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects")
	TSubclassOf<UGameplayEffect> BurnDebuffClass;

	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects")
	TSubclassOf<UGameplayEffect> ArcaneDebuffClass;

	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects")
	TSubclassOf<UGameplayEffect> PhysicalDebuffClass;


	/*
	 * Primary Attributes
	 */

	UPROPERTY
	(ReplicatedUsing = OnRep_Strength, BlueprintReadOnly, Category = "Primary Attributes")
	FGameplayAttributeData Strength;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, Strength);

	UPROPERTY(ReplicatedUsing = OnRep_Intelligence, BlueprintReadOnly, Category = "Primary Attributes")
	FGameplayAttributeData Intelligence;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, Intelligence);

	UPROPERTY(ReplicatedUsing = OnRep_Resilience, BlueprintReadOnly, Category = "Primary Attributes")
	FGameplayAttributeData Resilience;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, Resilience);

	UPROPERTY(ReplicatedUsing = OnRep_Vigor, BlueprintReadOnly, Category = "Primary Attributes")
	FGameplayAttributeData Vigor;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, Vigor);

	/*
	 * Secondary Attributes
	 */

	UPROPERTY(ReplicatedUsing = OnRep_Armor, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData Armor;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, Armor);

	UPROPERTY(ReplicatedUsing = OnRep_ArmorPenetration, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData ArmorPenetration;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, ArmorPenetration);

	UPROPERTY(ReplicatedUsing = OnRep_BlockChance, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData BlockChance;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, BlockChance);

	UPROPERTY(ReplicatedUsing = OnRep_CriticalHitChance, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData CriticalHitChance;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, CriticalHitChance);

	UPROPERTY(ReplicatedUsing = OnRep_CriticalHitDamage, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData CriticalHitDamage;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, CriticalHitDamage);

	UPROPERTY(ReplicatedUsing = OnRep_CriticalResistance, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData CriticalResistance;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, CriticalResistance);

	UPROPERTY(ReplicatedUsing = OnRep_HealthRegeneration, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData HealthRegeneration;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, HealthRegeneration);

	UPROPERTY(ReplicatedUsing = OnRep_ManaRegeneration, BlueprintReadOnly, Category = "Secondary Attributes")
	FGameplayAttributeData ManaRegeneration;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, ManaRegeneration);

	UPROPERTY(ReplicatedUsing = OnRep_MaxHealth, BlueprintReadOnly, Category = "Vital Attributes")
	FGameplayAttributeData MaxHealth;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, MaxHealth);

	UPROPERTY(ReplicatedUsing = OnRep_MaxMana, BlueprintReadOnly, Category = "Vital Attributes")
	FGameplayAttributeData MaxMana;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, MaxMana);

	/* 
	 * Damage Resistance Attributes
	 */

	UPROPERTY(ReplicatedUsing = OnRep_ResistanceFire, BlueprintReadOnly, Category = "Damage Resistance")
	FGameplayAttributeData ResistanceFire;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, ResistanceFire);

	UPROPERTY(ReplicatedUsing = OnRep_ResistanceLightning, BlueprintReadOnly, Category = "Damage Resistance")
	FGameplayAttributeData ResistanceLightning;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, ResistanceLightning);

	UPROPERTY(ReplicatedUsing = OnRep_ResistanceArcane, BlueprintReadOnly, Category = "Damage Resistance")
	FGameplayAttributeData ResistanceArcane;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, ResistanceArcane);

	UPROPERTY(ReplicatedUsing = OnRep_ResistancePhysical, BlueprintReadOnly, Category = "Damage Resistance")
	FGameplayAttributeData ResistancePhysical;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, ResistancePhysical);

	/*
	 * Vital Attributes
	 */

	UPROPERTY(ReplicatedUsing = OnRep_Health, BlueprintReadOnly, Category = "Vital Attributes")
	FGameplayAttributeData Health;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, Health);

	UPROPERTY(ReplicatedUsing = OnRep_Mana, BlueprintReadOnly, Category = "Vital Attributes")
	FGameplayAttributeData Mana;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, Mana);

	/*
	 * Damage Meta Attributes
	 */

	UPROPERTY(BlueprintReadOnly, Category = "Damage Meta Attributes")
	FGameplayAttributeData IncomingDamage;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, IncomingDamage);

	/*
	 * XP Meta Attributes
	 */
	UPROPERTY(BlueprintReadOnly, Category = "XP Meta Attributes")
	FGameplayAttributeData IncomingXP;
	ATTRIBUTE_ACCESSORS(UAuraAttributeSet, IncomingXP);


	UFUNCTION()
	void OnRep_Health(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_MaxHealth(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_Mana(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_MaxMana(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_Strength(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_Intelligence(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_Resilience(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_Vigor(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_Armor(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_ArmorPenetration(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_BlockChance(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_CriticalHitChance(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_CriticalHitDamage(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_CriticalResistance(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_HealthRegeneration(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_ManaRegeneration(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_ResistanceFire(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_ResistanceLightning(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_ResistanceArcane(const FGameplayAttributeData& OldValue) const;
	UFUNCTION()
	void OnRep_ResistancePhysical(const FGameplayAttributeData& OldValue) const;

private:
	static void SetEffectProperties(const struct FGameplayEffectModCallbackData& Data, FEffectProperties& Props);
	void SendXPEvent(const FEffectProperties& Props) const;
	void HandleIncomingDamage( FEffectProperties& Props);
	void ShowDamageText(const FEffectProperties& Props, float LocalIncomingDamage);
	void HandleIncomingXP(const FEffectProperties& Props);
	void Debuff(const FEffectProperties& Props);

	UPROPERTY()
	TArray<UGameplayEffect*> ActiveDynamicEffects;
};
