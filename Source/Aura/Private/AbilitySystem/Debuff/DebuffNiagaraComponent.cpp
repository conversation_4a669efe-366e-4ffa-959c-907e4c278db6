// Copyright iYuuki Studio


#include "AbilitySystem/Debuff/DebuffNiagaraComponent.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystemComponent.h"
#include "Interaction/CombatInterface.h"

UDebuffNiagaraComponent::UDebuffNiagaraComponent()
{
	bAutoActivate = false;
}

void UDebuffNiagaraComponent::BeginPlay()
{
	Super::BeginPlay();

	UAbilitySystemComponent* OwnerASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(GetOwner());

	ICombatInterface* CombatInterface = Cast<ICombatInterface>(GetOwner());
	if (OwnerASC)
	{
		OwnerASC->RegisterGameplayTagEvent(DebuffTag, EGameplayTagEventType::NewOrRemoved).AddUObject(
			this, &ThisClass::OnDebuffTagChanged);
	}
	else
	{
		if (CombatInterface)
			CombatInterface->GetOnRegisteredASCDelegate().AddWeakLambda(
				this,
				[this](UAbilitySystemComponent* ASC)
				{
					ASC->RegisterGameplayTagEvent(DebuffTag, EGameplayTagEventType::NewOrRemoved).AddUObject(
						this, &ThisClass::OnDebuffTagChanged);
				}
			);
	}

	if (CombatInterface)
	{
		CombatInterface->GetOnDieDelegate().AddDynamic(this, &ThisClass::OnDie);
	}
}


void UDebuffNiagaraComponent::OnDebuffTagChanged(FGameplayTag Tag, int32 NewCount)
{
	if (Tag != DebuffTag) return;

	if (NewCount > 0)
	{
		if (bIsDebuffed) return;
		Activate();
		bIsDebuffed = true;
	}
	else
	{
		Deactivate();
		bIsDebuffed = false;
	}
}

void UDebuffNiagaraComponent::OnDie()
{
	Deactivate();
}
