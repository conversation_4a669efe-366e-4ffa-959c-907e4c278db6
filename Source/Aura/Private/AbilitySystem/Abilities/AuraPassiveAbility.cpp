// Copyright iYuuki Studio


#include "AbilitySystem/Abilities/AuraPassiveAbility.h"

#include "AbilitySystem/AuraAbilitySystemComponent.h"

void UAuraPassiveAbility::ActivateAbility(const FGameplayAbilitySpecHandle Handle,
                                          const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo,
                                          const FGameplayEventData* TriggerEventData)
{
	Super::ActivateAbility(<PERSON><PERSON>, ActorInfo, ActivationInfo, TriggerEventData);

	if (UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(ActorInfo->AbilitySystemComponent.Get()))
	{
		AuraASC->OnDeactivatePassiveAbilityDelegate.AddUObject(this, &ThisClass::OnDeactivatePassiveAbility);
	}
}

void UAuraPassiveAbility::OnDeactivatePassiveAbility(const FGameplayTag& AbilityTag)
{
	if (AbilityTags.HasTagExact(AbilityTag))
	{
		EndAbility(CurrentSpec<PERSON>and<PERSON>, CurrentActorInfo, CurrentActivationInfo, true, true);
	}
}
