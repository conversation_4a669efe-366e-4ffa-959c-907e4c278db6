// Copyright iYuuki Studio


#include "AbilitySystem/Passive/AuraPassiveNiagaraComponent.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystem/AuraAbilitySystemComponent.h"
#include "Aura/AuraLogChannel.h"
#include "Interaction/CombatInterface.h"

UAuraPassiveNiagaraComponent::UAuraPassiveNiagaraComponent()
{
	bAutoActivate = false;
}

void UAuraPassiveNiagaraComponent::BeginPlay()
{
	Super::BeginPlay();

	ICombatInterface* CombatInterface = Cast<ICombatInterface>(GetOwner());
	if (UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(
		UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(GetOwner())))
	{
		AuraASC->OnPassiveAbilityStatusChangedDelegate.AddUObject(this, &ThisClass::OnStatusChanged);
	}
	else
	{
		if (CombatInterface)
		{
			CombatInterface->GetOnRegisteredASCDelegate().AddWeakLambda(
				this,
				[this](UAbilitySystemComponent* ASC)
				{
					if (UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(ASC))
					{
						AuraASC->OnPassiveAbilityStatusChangedDelegate.AddUObject(this, &ThisClass::OnStatusChanged);
						UE_LOG(LogAura, Log, TEXT("OnRegisteredASCDelegate"));
					}
				}
			);
		}
	}

	if (CombatInterface)
	{
		CombatInterface->GetOnDieDelegate().AddDynamic(this, &ThisClass::OnDie);
	}
}

void UAuraPassiveNiagaraComponent::OnStatusChanged(const FGameplayTag& InAbilityTag, const bool bActivate)
{
	if (AbilityTag.MatchesTagExact(InAbilityTag))
	{
		if (bActivate && IsActive() == false)
		{
			Activate();
		}
		else 
		{
			Deactivate();
		}
	}
}

void UAuraPassiveNiagaraComponent::OnDie()
{
	Deactivate();
}
