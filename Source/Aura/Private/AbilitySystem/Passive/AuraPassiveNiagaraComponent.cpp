// Copyright iYuuki Studio


#include "AbilitySystem/Passive/AuraPassiveNiagaraComponent.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystem/AuraAbilitySystemComponent.h"
#include "Aura/AuraLogChannel.h"
#include "Interaction/CombatInterface.h"

UAuraPassiveNiagaraComponent::UAuraPassiveNiagaraComponent()
{
	bAutoActivate = false;

	// 设置组件复制，确保激活状态在所有客户端同步
	SetIsReplicated(true);
}

void UAuraPassiveNiagaraComponent::BeginPlay()
{
	Super::BeginPlay();

	ICombatInterface* CombatInterface = Cast<ICombatInterface>(GetOwner());
	if (UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(
		UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(GetOwner())))
	{
		AuraASC->OnPassiveAbilityStatusChangedDelegate.AddUObject(this, &ThisClass::OnStatusChanged);
	}
	else
	{
		if (CombatInterface)
		{
			CombatInterface->GetOnRegisteredASCDelegate().AddWeakLambda(
				this,
				[this](UAbilitySystemComponent* ASC)
				{
					if (UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(ASC))
					{
						AuraASC->OnPassiveAbilityStatusChangedDelegate.AddUObject(this, &ThisClass::OnStatusChanged);
						UE_LOG(LogAura, Log, TEXT("OnRegisteredASCDelegate"));
					}
				}
			);
		}
	}

	if (CombatInterface)
	{
		CombatInterface->GetOnDieDelegate().AddDynamic(this, &ThisClass::OnDie);
	}
}

void UAuraPassiveNiagaraComponent::OnStatusChanged(const FGameplayTag& InAbilityTag, const bool bActivate)
{
	if (AbilityTag.MatchesTagExact(InAbilityTag))
	{
		UE_LOG(LogAura, Warning, TEXT("OnStatusChanged called for %s, bActivate: %s, IsActive: %s, NetRole: %s"),
			*InAbilityTag.ToString(),
			bActivate ? TEXT("true") : TEXT("false"),
			IsActive() ? TEXT("true") : TEXT("false"),
			*UEnum::GetValueAsString(GetOwner()->GetLocalRole()));

		if (bActivate && IsActive() == false)
		{
			Activate();
			UE_LOG(LogAura, Warning, TEXT("Activated PassiveNiagaraComponent for %s"), *InAbilityTag.ToString());
		}
		else if (!bActivate && IsActive())
		{
			Deactivate();
			UE_LOG(LogAura, Warning, TEXT("Deactivated PassiveNiagaraComponent for %s"), *InAbilityTag.ToString());
		}
	}
}

void UAuraPassiveNiagaraComponent::OnDie()
{
	Deactivate();
}
