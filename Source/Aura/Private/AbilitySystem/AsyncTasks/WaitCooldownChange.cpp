#include "AbilitySystem/AsyncTasks/WaitCooldownChange.h"
#include "AbilitySystemComponent.h"

UWaitCooldownChange* UWaitCooldownChange::WaitCooldownChange(const FGameplayTag& InCooldownTag,
                                                             UAbilitySystemComponent* ASC)
{
	UWaitCooldownChange* Task = NewObject<UWaitCooldownChange>();
	Task->AbilitySystemComponent = ASC;

	if (!IsValid(ASC) || !InCooldownTag.IsValid())
	{
		Task->EndWaitCooldownChange();
		return Task;
	}

	// 保存委托句柄以便后续清理
	Task->EffectAddedDelegateHandle = ASC->OnActiveGameplayEffectAddedDelegateToSelf.AddLambda(
		[Task, InCooldownTag](UAbilitySystemComponent* InASC, const FGameplayEffectSpec& Spec,
		                      FActiveGameplayEffectHandle Handle)
		{
			// 检查 Task 是否仍然有效，防止访问悬空指针
			if (!IsValid(Task))
			{
				return;
			}

			FGameplayTagContainer AssetTags;
			Spec.GetAllAssetTags(AssetTags);

			FGameplayTagContainer GrantedTags;
			Spec.GetAllGrantedTags(GrantedTags);

			if (AssetTags.HasTagExact(InCooldownTag) || GrantedTags.HasTagExact(InCooldownTag))
			{
				FGameplayEffectQuery Query = FGameplayEffectQuery::MakeQuery_MatchAnyOwningTags(
					InCooldownTag.GetSingleTagContainer());

				TArray<float> Durations = InASC->GetActiveEffectsTimeRemaining(Query);
				if (Durations.Num() > 0)
				{
					float Remaining = Durations[0];
					for (int i = 1; i < Durations.Num(); ++i)
					{
						Remaining = FMath::Max(Remaining, Durations[i]);
					}
					// 再次检查 Task 有效性，因为在多线程环境中对象可能在执行过程中被销毁
					if (IsValid(Task))
					{
						Task->OnCooldownStart.Broadcast(Remaining);
					}
				}
			}
		});

	// 注册冷却标签变化（新增/移除）
	Task->CooldownEventHandle = ASC->RegisterGameplayTagEvent(
		InCooldownTag, EGameplayTagEventType::NewOrRemoved).AddUObject(
		Task, &UWaitCooldownChange::CooldownTagChanged);

	return Task;
}

void UWaitCooldownChange::CooldownTagChanged(FGameplayTag Tag, int32 NewCount)
{
	if (!Tag.IsValid()) return;

	if (NewCount <= 0)
	{
		OnCooldownEnd.Broadcast(0.f);
	}
}

void UWaitCooldownChange::EndWaitCooldownChange()
{
	if (IsValid(AbilitySystemComponent))
	{
		// 不再使用 RemoveAll，避免移除其他任务注册的委托
		AbilitySystemComponent->RegisterGameplayTagEvent(FGameplayTag(), EGameplayTagEventType::NewOrRemoved)
		                      .Remove(CooldownEventHandle);
	}

	SetReadyToDestroy();
	MarkAsGarbage();
}
