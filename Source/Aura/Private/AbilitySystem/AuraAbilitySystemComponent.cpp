// Copyright iYuuki Studio


#include "AbilitySystem/AuraAbilitySystemComponent.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AuraGameplayTags.h"
#include "AbilitySystem/Abilities/AuraGameplayAbility.h"
#include "Aura/AuraLogChannel.h"
#include "Interaction/PlayerInterface.h"
#include "System/AuraAbilitySystemLibrary.h"

void UAuraAbilitySystemComponent::AbilityActorInfoSet()
{
	OnGameplayEffectAppliedDelegateToSelf.AddUObject(this, &ThisClass::Client_OnGameplayEffectApplied);
}

void UAuraAbilitySystemComponent::AddCharacterAbilities(const TArray<TSubclassOf<UGameplayAbility>>& StartupAbilities)
{
	for (const TSubclassOf<UGameplayAbility>& AbilityClass : StartupAbilities)
	{
		FGameplayAbilitySpec AbilitySpec = FGameplayAbilitySpec(AbilityClass, 1);
		UAuraGameplayAbility* AuraAbility = CastChecked<UAuraGameplayAbility>(AbilitySpec.Ability);
		AbilitySpec.GetDynamicSpecSourceTags().AddTag(AuraAbility->StartupInputTag);
		AbilitySpec.GetDynamicSpecSourceTags().AddTag(FAuraGameplayTags::Get().Abilities_Status_Equipped);
		GiveAbility(AbilitySpec);
	}

	bStartupAbilitiesGiven = true;
	OnGivenAbility.Broadcast();
}

void UAuraAbilitySystemComponent::AddCharacterPassiveAbilities(
	const TArray<TSubclassOf<UGameplayAbility>>& PassiveAbilities)
{
	for (const TSubclassOf<UGameplayAbility>& AbilityClass : PassiveAbilities)
	{
		FGameplayAbilitySpec AbilitySpec = FGameplayAbilitySpec(AbilityClass, 1);
		GiveAbilityAndActivateOnce(AbilitySpec);
	}
}

void UAuraAbilitySystemComponent::UpdateAbilityStatusByLevel(int32 Level)
{
	UAbilityInfo* AbilityInfo = UAuraAbilitySystemLibrary::GetAbilityInformation(this);
	for (const auto& Row : AbilityInfo->AbilityInformation)
	{
		if (!Row.AbilityTag.IsValid()) continue;
		if (Row.LevelRequired > Level) continue;
		if (GetSpecFromAbilityTag(Row.AbilityTag) != nullptr) continue;
		FGameplayAbilitySpec AbilitySpec = FGameplayAbilitySpec(Row.Ability, 1);
		FGameplayTagContainer& Tags = AbilitySpec.GetDynamicSpecSourceTags();
		Tags.AddTag(FAuraGameplayTags::Get().Abilities_Status_Eligible);
		GiveAbility(AbilitySpec);
		MarkAbilitySpecDirty(AbilitySpec);
		Client_OnAbilityStatusChanged(Row.AbilityTag, FAuraGameplayTags::Get().Abilities_Status_Eligible, 1);
	}
}


void UAuraAbilitySystemComponent::AbilityInputTagPressed(const FGameplayTag& InputTag)
{
	if (!InputTag.IsValid()) return;

	FScopedAbilityListLock ActiveScopeLock(*this);
	for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
	{
		if (AbilitySpec.GetDynamicSpecSourceTags().HasTagExact(InputTag))
		{
			if (AbilitySpec.IsActive())
			{
				UGameplayAbility* PrimaryInstance = AbilitySpec.GetPrimaryInstance();
				if (PrimaryInstance)
				{
					InvokeReplicatedEvent(EAbilityGenericReplicatedEvent::InputPressed, AbilitySpec.Handle,
					                      PrimaryInstance->GetCurrentActivationInfo().GetActivationPredictionKey());
				}
			}
		}
	}
}

void UAuraAbilitySystemComponent::AbilityInputTagHeld(const FGameplayTag& InputTag)
{
	if (!InputTag.IsValid()) return;

	FScopedAbilityListLock ActiveScopeLock(*this);
	for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
	{
		if (AbilitySpec.GetDynamicSpecSourceTags().HasTagExact(InputTag))
		{
			AbilitySpecInputPressed(AbilitySpec);
			if (!AbilitySpec.IsActive())
			{
				TryActivateAbility(AbilitySpec.Handle);
			}
		}
	}
}

void UAuraAbilitySystemComponent::AbilityInputTagReleased(const FGameplayTag& InputTag)
{
	FScopedAbilityListLock ActiveScopeLock(*this);
	for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
	{
		if (AbilitySpec.GetDynamicSpecSourceTags().HasTagExact(InputTag))
		{
			AbilitySpecInputReleased(AbilitySpec);

			if (AbilitySpec.IsActive())
			{
				// used for UE5.5. UE5.5 is not support AbilitySpec.ActivationInfo.GetActivationPredictionKey()
				const UGameplayAbility* PrimaryInstance = AbilitySpec.GetPrimaryInstance();
				if (PrimaryInstance)
				{
					InvokeReplicatedEvent(EAbilityGenericReplicatedEvent::InputReleased, AbilitySpec.Handle,
					                      PrimaryInstance->GetCurrentActivationInfo().GetActivationPredictionKey());
				}
			}
		}
	}
}

void UAuraAbilitySystemComponent::ForEachAbility(FForEachAbility Delegate)
{
	FScopedAbilityListLock ActiveScopeLock(*this);
	for (const FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
	{
		if (!Delegate.ExecuteIfBound(AbilitySpec))
		{
			UE_LOG(LogAura, Error, TEXT("Failed to execute delegate for ability %s"), *AbilitySpec.Ability->GetName());
		}
	}
}

FGameplayTag UAuraAbilitySystemComponent::GetAbilityTagFromSpec(const FGameplayAbilitySpec& AbilitySpec)
{
	for (const FGameplayTag& Tag : AbilitySpec.Ability->GetAssetTags())
	{
		if (Tag.MatchesTag(FGameplayTag::RequestGameplayTag(FName("Abilities"))))
		{
			return Tag;
		}
	}
	return FGameplayTag();
}

FGameplayTag UAuraAbilitySystemComponent::GetInputTagFromSpec(const FGameplayAbilitySpec& AbilitySpec)
{
	for (const FGameplayTag& Tag : AbilitySpec.GetDynamicSpecSourceTags())
	{
		if (Tag.MatchesTag(FGameplayTag::RequestGameplayTag(FName("InputTag"))))
		{
			return Tag;
		}
	}
	return FGameplayTag();
}

FGameplayTag UAuraAbilitySystemComponent::GetStatusTagFromSpec(const FGameplayAbilitySpec& AbilitySpec)
{
	if (&AbilitySpec == nullptr || !AbilitySpec.Ability) return FAuraGameplayTags::Get().Abilities_Status_Locked;

	for (const FGameplayTag& Tag : AbilitySpec.GetDynamicSpecSourceTags())
	{
		if (Tag.MatchesTag(FGameplayTag::RequestGameplayTag(FName("Abilities.Status"))))
		{
			return Tag;
		}
	}
	return FAuraGameplayTags::Get().Abilities_Status_Locked;
}

FGameplayTag UAuraAbilitySystemComponent::GetTypeTagFromSpec(const FGameplayAbilitySpec& AbilitySpec)
{
	UAbilityInfo* AbilityInfo = UAuraAbilitySystemLibrary::GetAbilityInformation(this);
	if (!AbilityInfo) return FGameplayTag();
	FAuraAbilityInfo AuraAbilityInfo = AbilityInfo->FindAbilityInfoForTag(GetAbilityTagFromSpec(AbilitySpec));
	return AuraAbilityInfo.AbilityType;
}

FGameplayTag UAuraAbilitySystemComponent::GetStatusTagFromAbilityTag(const FGameplayTag& AbilityTag)
{
	FGameplayAbilitySpec* AbilitySpec = GetSpecFromAbilityTag(AbilityTag);
	return GetStatusTagFromSpec(*AbilitySpec);
}

FGameplayAbilitySpec* UAuraAbilitySystemComponent::GetSpecFromAbilityTag(const FGameplayTag& AbilityTag)
{
	FScopedAbilityListLock ActiveScopeLock(*this);
	for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
	{
		if (AbilitySpec.Ability->GetAssetTags().HasTagExact(AbilityTag))
		{
			return &AbilitySpec;
		}
	}
	return nullptr;
}

FGameplayTag UAuraAbilitySystemComponent::GetTypeTagFromAbilityTag(const FGameplayTag& AbilityTag)
{
	FGameplayAbilitySpec* AbilitySpec = GetSpecFromAbilityTag(AbilityTag);
	if (AbilitySpec == nullptr) return FGameplayTag();

	UAbilityInfo* AbilityInfo = UAuraAbilitySystemLibrary::GetAbilityInformation(this);
	FAuraAbilityInfo AuraAbilityInfo = AbilityInfo->FindAbilityInfoForTag(AbilityTag);
	return AuraAbilityInfo.AbilityType;
}

FGameplayAbilitySpec* UAuraAbilitySystemComponent::GetSpecFromInputTag(const FGameplayTag& InputTag)
{
	UAbilityInfo* AbilityInfo = UAuraAbilitySystemLibrary::GetAbilityInformation(this);
	if (!AbilityInfo) return nullptr;

	FScopedAbilityListLock ActiveScopeLock(*this);
	for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
	{
		if (AbilitySpec.GetDynamicSpecSourceTags().HasTagExact(InputTag))
		{
			return &AbilitySpec;
		}
	}
	return nullptr;
}

void UAuraAbilitySystemComponent::UpgradeAttribute_Implementation(const FGameplayTag& AttributeTag)
{
	FGameplayEventData Payload;
	Payload.EventTag = AttributeTag;
	Payload.EventMagnitude = 1.f;
	UAbilitySystemBlueprintLibrary::SendGameplayEventToActor(GetAvatarActor(), AttributeTag, Payload);

	if (GetAvatarActor()->Implements<UPlayerInterface>())
	{
		IPlayerInterface::Execute_AddToAttributePoints(GetAvatarActor(), -1);
	}
}

void UAuraAbilitySystemComponent::Server_SpendSpellPoint_Implementation(const FGameplayTag& AbilityTag)
{
	FGameplayAbilitySpec* AbilitySpec = GetSpecFromAbilityTag(AbilityTag);
	if (AbilitySpec == nullptr) return;

	FGameplayTag StatusTag = GetStatusTagFromSpec(*AbilitySpec);
	const FAuraGameplayTags GameplayTags = FAuraGameplayTags::Get();
	if (StatusTag.MatchesTag(GameplayTags.Abilities_Status_Eligible))
	{
		FGameplayTagContainer& Tags = AbilitySpec->GetDynamicSpecSourceTags();
		Tags.RemoveTag(GameplayTags.Abilities_Status_Eligible);
		Tags.AddTag(GameplayTags.Abilities_Status_Unlocked);
		StatusTag = GameplayTags.Abilities_Status_Unlocked;
	}
	else if (StatusTag.MatchesTag(GameplayTags.Abilities_Status_Equipped) || StatusTag.MatchesTag(
		GameplayTags.Abilities_Status_Unlocked))
	{
		// Ability Level++
		AbilitySpec->Level += 1;
	}
	MarkAbilitySpecDirty(*AbilitySpec);
	Client_OnAbilityStatusChanged(AbilityTag, StatusTag, AbilitySpec->Level);

	if (GetAvatarActor()->Implements<UPlayerInterface>())
	{
		IPlayerInterface::Execute_AddToSpellPoints(GetAvatarActor(), -1);
	}
}

void UAuraAbilitySystemComponent::Server_SelectSpell_Implementation(const FGameplayTag& AbilityTag)
{
	FString Description;
	FString NextLevelDescription;
	GetAbilityDescriptions(AbilityTag, Description, NextLevelDescription);
	Client_OnSelectSpell(AbilityTag, Description, NextLevelDescription);
}

void UAuraAbilitySystemComponent::Server_WaitingForEquipAbility_Implementation(const FGameplayTag& AbilityTag)
{
	FGameplayTag TypeTag = GetTypeTagFromAbilityTag(AbilityTag);
	if (TypeTag == FGameplayTag()) return;
	Client_OnWaitingForEquipAbility(AbilityTag, TypeTag);
}

void UAuraAbilitySystemComponent::Server_EquipAbility_Implementation(const FGameplayTag& AbilityTag,
                                                                     const FGameplayTag& SlotTag)
{
	FGameplayTag TypeTag = GetTypeTagFromAbilityTag(AbilityTag);
	if (TypeTag == FGameplayTag()) return; // Invalid Tag

	UAbilityInfo* AbilityInfo = UAuraAbilitySystemLibrary::GetAbilityInformation(this);
	FAuraAbilityInfo AuraAbilityInfo = AbilityInfo->FindAbilityInfoForTag(AbilityTag);

	FGameplayAbilitySpec* AbilitySpec = GetSpecFromAbilityTag(AbilityTag);
	if (AbilitySpec == nullptr) return;

	FGameplayTagContainer& AbilityTags = AbilitySpec->GetDynamicSpecSourceTags();

	if (SlotTag.MatchesTag(FGameplayTag::RequestGameplayTag(FName("InputTag.Passive"))))
	{
		if (!TypeTag.MatchesTagExact(FAuraGameplayTags::Get().Abilities_Type_Passive))return;
	}
	else if (SlotTag.MatchesTag(FGameplayTag::RequestGameplayTag(FName("InputTag"))))
	{
		if (!TypeTag.MatchesTagExact(FAuraGameplayTags::Get().Abilities_Type_Offensive))return;
	}

	bool bAlreadyEquipped = false;

	const FGameplayTag& OriginalSlotTag = GetInputTagFromSpec(*AbilitySpec);
	if (OriginalSlotTag != FGameplayTag()) // OriginalSlotTag from old slot is not empty
	{
		AbilityTags.RemoveTag(OriginalSlotTag);
		Client_OnClearEquippedAbility(OriginalSlotTag);
		bAlreadyEquipped = true;
	}

	FGameplayAbilitySpec* OldAbilitySpec = GetSpecFromInputTag(SlotTag);
	if (OldAbilitySpec != nullptr) // OldAbilitySpec in the current slot is not nullptr
	{
		FGameplayTagContainer& OldTags = OldAbilitySpec->GetDynamicSpecSourceTags();
		OldTags.RemoveTag(SlotTag);
		MarkAbilitySpecDirty(*OldAbilitySpec);
		Client_OnClearEquippedAbility(SlotTag);

		FGameplayTag OldAbilityType = GetTypeTagFromSpec(*OldAbilitySpec);
		if (OldAbilityType.MatchesTagExact(FAuraGameplayTags::Get().Abilities_Type_Passive))
		{
			FGameplayTag OldTag = GetAbilityTagFromSpec(*OldAbilitySpec);
			OnDeactivatePassiveAbilityDelegate.Broadcast(OldTag);
			Client_OnPassiveAbilityStatusChanged(OldTag, false);
		}
	}

	AbilityTags.AddTag(SlotTag);
	AuraAbilityInfo.InputTag = SlotTag;
	AuraAbilityInfo.StatusTag = FAuraGameplayTags::Get().Abilities_Status_Equipped;
	MarkAbilitySpecDirty(*AbilitySpec);
	Client_OnEquipAbility(AuraAbilityInfo, AbilitySpec->Level);

	FGameplayTag AbilityType = GetTypeTagFromSpec(*AbilitySpec);
	if (AbilityType.MatchesTagExact(FAuraGameplayTags::Get().Abilities_Type_Passive) && !bAlreadyEquipped)
	{
		TryActivateAbility(AbilitySpec->Handle);
		Client_OnPassiveAbilityStatusChanged(AbilityTag, true);
	}
}

bool UAuraAbilitySystemComponent::GetAbilityDescriptions(const FGameplayTag& AbilityTag,
                                                         FString& Description, FString& NextLevelDescription)
{
	FGameplayAbilitySpec* AbilitySpec = GetSpecFromAbilityTag(AbilityTag);
	if (AbilitySpec == nullptr)
	{
		if (UAuraAbilitySystemLibrary::GetAbilityInformation(this) == nullptr) return false;

		Description = UAuraGameplayAbility::GetLockedDescription(
			UAuraAbilitySystemLibrary::GetAbilityInformation(this)->
			FindAbilityInfoForTag(AbilityTag).LevelRequired);
		return false;
	}
	UAuraGameplayAbility* AuraAbility = CastChecked<UAuraGameplayAbility>(AbilitySpec->Ability);
	Description = AuraAbility->GetDescription(AbilitySpec->Level);
	NextLevelDescription = AuraAbility->GetNextLevelDescription(AbilitySpec->Level + 1);
	return true;
}

void UAuraAbilitySystemComponent::OnRep_ActivateAbilities()
{
	Super::OnRep_ActivateAbilities();
	if (!bStartupAbilitiesGiven)
	{
		bStartupAbilitiesGiven = true;
		OnGivenAbility.Broadcast();
	}
}

void UAuraAbilitySystemComponent::Client_OnPassiveAbilityStatusChanged_Implementation(const FGameplayTag& AbilityTag,
	const bool bActivate)
{
	OnPassiveAbilityStatusChangedDelegate.Broadcast(AbilityTag, bActivate);
}

void UAuraAbilitySystemComponent::Client_OnClearEquippedAbility_Implementation(const FGameplayTag& SlotTag)
{
	OnClearEquippedAbilityDelegate.Broadcast(SlotTag);
}

void UAuraAbilitySystemComponent::Client_OnEquipAbility_Implementation(const FAuraAbilityInfo& Info,
                                                                       const int32 AbilityLevel)
{
	OnEquippedAbilityStatusChanged.Broadcast(Info, AbilityLevel);
}

void UAuraAbilitySystemComponent::Client_OnWaitingForEquipAbility_Implementation(const FGameplayTag& AbilityTag,
	const FGameplayTag& TypeTag)
{
	OnWaitingForAbilityDelegate.Broadcast(AbilityTag, TypeTag);
}

void UAuraAbilitySystemComponent::Client_OnSelectSpell_Implementation(const FGameplayTag& AbilityTag,
                                                                      const FString& Description,
                                                                      const FString& NextLevelDescription)
{
	OnActivateAbilityButton.Broadcast(AbilityTag, Description, NextLevelDescription);
}

void UAuraAbilitySystemComponent::Client_OnAbilityStatusChanged_Implementation(const FGameplayTag& AbilityTag,
                                                                               const FGameplayTag& StatusTag,
                                                                               const int32 Level)
{
	OnAbilityStatusChanged.Broadcast(AbilityTag, StatusTag, Level);
}

void UAuraAbilitySystemComponent::Client_OnGameplayEffectApplied_Implementation(UAbilitySystemComponent* ASC,
	const FGameplayEffectSpec& EffectSpec, FActiveGameplayEffectHandle ActiveGameplayEffectHandle)
{
	FGameplayTagContainer Tags;
	EffectSpec.GetAllAssetTags(Tags);
	OnEffectAssets.Broadcast(Tags);
}
